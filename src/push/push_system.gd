class_name PushSystem
extends Node

@export var movement_system: MovementSystem

var _player_movement_component: MovementComponent

func _ready() -> void:
	var player: Node = get_tree().get_first_node_in_group("player")
	if player != null:
		_player_movement_component = player.find_child("MovementComponent", true, false)
		if _player_movement_component != null and _player_movement_component.data != null:
			_player_movement_component.data.movement_blocked.connect(_on_player_blocked)

func _on_player_blocked(direction: Vector2) -> void:
	if _player_movement_component == null or _player_movement_component.collision_ray == null:
		return

	var ray: RayCast2D = _player_movement_component.collision_ray
	if not ray.is_colliding():
		return

	var collider: Object = ray.get_collider()
	if collider == null:
		return
	var obstacle: Node = (collider as Node).get_parent()
	if obstacle == null:
		return

	var pushable: PushableComponent = obstacle.find_child("PushableComponent", true, false)
	if pushable == null:
		return

	var obstacle_movement: MovementComponent = obstacle.find_child("MovementComponent", true, false)
	if obstacle_movement == null or obstacle_movement.data == null:
		return

	if obstacle_movement.data.is_moving:
		return

	var eraser_component: EraserComponent = obstacle.find_child("EraserComponent", true, false)
	if eraser_component != null and eraser_component.state == EraserComponent.State.FLEE:
		return

	var obstacle_ray: RayCast2D = obstacle_movement.collision_ray
	if obstacle_ray == null:
		return

	var target_pos: Vector2 = direction * obstacle_movement.data.tile_size
	obstacle_ray.target_position = target_pos
	obstacle_ray.force_raycast_update()

	if obstacle_ray.is_colliding():
		return

	movement_system.move(obstacle_movement, direction)

	await obstacle_movement.data.movement_completed

	if _player_movement_component != null and _player_movement_component.data != null:
		movement_system.move(_player_movement_component, direction)
