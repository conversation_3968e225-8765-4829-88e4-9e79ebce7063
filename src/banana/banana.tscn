[gd_scene load_steps=21 format=3 uid="uid://dq0bjiqkyl4wv"]

[ext_resource type="Texture2D" uid="uid://cj35vjs6j4qr3" path="res://assets/banana.png" id="1_0fpyy"]
[ext_resource type="Script" uid="uid://bwkg6ydcldanv" path="res://src/banana/banana.gd" id="1_a7o36"]
[ext_resource type="Script" uid="uid://chn55a5u5s7ol" path="res://src/health/health_data.gd" id="3_xits2"]
[ext_resource type="PackedScene" uid="uid://b2minqce3p1bg" path="res://src/health/health_component.tscn" id="4_xi0yy"]
[ext_resource type="Script" uid="uid://s041hcolx1gq" path="res://src/paint/paint_data.gd" id="5_fgh8s"]
[ext_resource type="PackedScene" path="res://src/paint/paint_component.tscn" id="5_paint_component"]
[ext_resource type="PackedScene" uid="uid://d0ntxy5trg0re" path="res://src/ability/ability_component.tscn" id="6_ability_component"]
[ext_resource type="Script" uid="uid://72hm77eoq5jd" path="res://src/ability/ability_data.gd" id="7_vqmnj"]
[ext_resource type="Script" uid="uid://dcms2verply6s" path="res://src/ability/restore_paint_ability_data.gd" id="8_vqmnj"]
[ext_resource type="Script" uid="uid://5g3i8wjai3b2" path="res://src/ability/maintain_normal_time_ability_data.gd" id="9_sh2yo"]
[ext_resource type="Script" uid="uid://b5xc2p1kg3ho6" path="res://src/ability/wall_phase_ability_data.gd" id="10_aco0u"]
[ext_resource type="Script" uid="uid://bkusl4o508on1" path="res://src/ability/multi_drop_ability_data.gd" id="11_multi_drop"]
[ext_resource type="PackedScene" uid="uid://k2v1icx8llrc" path="res://src/movement/movement_component.tscn" id="12_aco0u"]

[sub_resource type="Resource" id="Resource_gmdx1"]
script = ExtResource("3_xits2")
max_health = 2
current_health = 2
metadata/_custom_type_script = "uid://chn55a5u5s7ol"

[sub_resource type="Resource" id="Resource_85iae"]
script = ExtResource("5_fgh8s")
paint_color = Color(1, 0.914, 0.133, 1)
max_paint = 10
metadata/_custom_type_script = "uid://s041hcolx1gq"

[sub_resource type="Resource" id="Resource_sh2yo"]
script = ExtResource("8_vqmnj")
paint_to_restore = 1
metadata/_custom_type_script = "uid://dcms2verply6s"

[sub_resource type="Resource" id="Resource_aco0u"]
script = ExtResource("9_sh2yo")
metadata/_custom_type_script = "uid://5g3i8wjai3b2"

[sub_resource type="Resource" id="Resource_cfk8s"]
script = ExtResource("10_aco0u")
paint_cost = 1
metadata/_custom_type_script = "uid://b5xc2p1kg3ho6"

[sub_resource type="Resource" id="Resource_multi_drop"]
script = ExtResource("11_multi_drop")
drop_count = 3

[sub_resource type="CircleShape2D" id="CircleShape2D_0fpyy"]
radius = 3.0

[node name="Banana" type="CharacterBody2D" node_paths=PackedStringArray("paint_component") groups=["player"]]
motion_mode = 1
script = ExtResource("1_a7o36")
paint_component = NodePath("PaintComponent")

[node name="HealthComponent" parent="." instance=ExtResource("4_xi0yy")]
data = SubResource("Resource_gmdx1")

[node name="PaintComponent" parent="." instance=ExtResource("5_paint_component")]
data = SubResource("Resource_85iae")

[node name="AbilityComponent" parent="." instance=ExtResource("6_ability_component")]
abilities = Array[ExtResource("7_vqmnj")]([SubResource("Resource_sh2yo"), SubResource("Resource_aco0u"), SubResource("Resource_cfk8s"), SubResource("Resource_multi_drop")])

[node name="MovementComponent" parent="." node_paths=PackedStringArray("actor", "collision_ray") instance=ExtResource("12_aco0u")]
actor = NodePath("..")
collision_ray = NodePath("../CollisionRay")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture_filter = 1
position = Vector2(0, -4)
texture = ExtResource("1_0fpyy")

[node name="Collision" type="CollisionShape2D" parent="."]
position = Vector2(1, -4)
shape = SubResource("CircleShape2D_0fpyy")
debug_color = Color(0.282804, 0.501787, 0.99058, 0.666667)

[node name="Hitbox" type="Area2D" parent="."]
position = Vector2(0, -4)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Hitbox"]
position = Vector2(1, 1)
shape = SubResource("CircleShape2D_0fpyy")
debug_color = Color(0.329214, 0.61418, 0.295959, 0.5)

[node name="CollisionRay" type="RayCast2D" parent="."]
target_position = Vector2(0, 0)
