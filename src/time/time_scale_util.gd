class_name TimeScaleUtil

static func get_time_scale(node: Node) -> float:
	if node == null:
		return 1.0
	
	var time_sensitive: TimeSensitiveComponent = _find_time_sensitive_component(node)
	if time_sensitive == null or time_sensitive.data == null:
		return 1.0
	
	var data: TimeSensitiveData = time_sensitive.data
	if data.normal_speed <= 0.0:
		return 1.0
	
	return data.current_speed / data.normal_speed

static func scale_delta(node: Node, base_delta: float) -> float:
	return base_delta * get_time_scale(node)

static func _find_time_sensitive_component(node: Node) -> TimeSensitiveComponent:
	var current: Node = node
	while current != null:
		var time_sensitive: TimeSensitiveComponent = current.find_child("TimeSensitiveComponent", true, false)
		if time_sensitive != null:
			return time_sensitive
		current = current.get_parent()
	return null
