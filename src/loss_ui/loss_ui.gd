extends <PERSON><PERSON><PERSON><PERSON><PERSON>

@export var play_again_button: But<PERSON>
@export var level_state_component: LevelStateComponent

func _ready() -> void:
	play_again_button.pressed.connect(_on_play_again_pressed)
	hide()

	if is_instance_valid(level_state_component) and is_instance_valid(level_state_component.data):
		level_state_component.data.state_changed.connect(_on_state_changed)

func _on_state_changed(new_state: LevelStateData.State) -> void:
	if new_state == LevelStateData.State.LOST:
		show_loss_screen()

func show_loss_screen() -> void:
	show()

func _on_play_again_pressed() -> void:
	get_tree().reload_current_scene()
