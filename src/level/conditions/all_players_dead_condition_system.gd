class_name AllPlayersDeadConditionSystem
extends ConditionSystem

@export var health_system: HealthSystem
@export var player_node: Node2D

func is_met() -> bool:
	if not is_instance_valid(player_node):
		return true

	var health_component: HealthComponent = player_node.find_child(&"HealthComponent", true, false)
	if not is_instance_valid(health_component):
		return true

	return health_system.is_dead(health_component.data)
