[gd_scene load_steps=46 format=4 uid="uid://bk5rydqrj5etw"]

[ext_resource type="Script" uid="uid://b5vf4g4ieswao" path="res://src/level/level.gd" id="1_5vox1"]
[ext_resource type="PackedScene" uid="uid://2o2nqedmdng0" path="res://src/color_tile/color_tile.tscn" id="2_5vox1"]
[ext_resource type="Texture2D" uid="uid://dvgogbmr7vcxt" path="res://assets/tiles_sheet.png" id="3_ob0n7"]
[ext_resource type="PackedScene" uid="uid://ce5u8lunfmkij" path="res://src/paint_bar/paint_bar.tscn" id="4_p6v4a"]
[ext_resource type="Script" uid="uid://d22krovyllkp2" path="res://src/level/progress_system/level_event_data.gd" id="4_y4oqq"]
[ext_resource type="PackedScene" uid="uid://bv3kc8uwrke6f" path="res://src/progress_bar/progress_bar.tscn" id="5_5suky"]
[ext_resource type="Texture2D" uid="uid://dunym6c7vtv8i" path="res://assets/cog_silver.png" id="5_u1qsv"]
[ext_resource type="PackedScene" uid="uid://dq0bjiqkyl4wv" path="res://src/banana/banana.tscn" id="6_k5trb"]
[ext_resource type="Script" uid="uid://daib4adyoymm7" path="res://src/level/conditions/paint_all_tiles_condition_system.gd" id="7_gj0ym"]
[ext_resource type="Texture2D" uid="uid://cm7p58sgwcf46" path="res://assets/cog_bronze.png" id="7_vgudg"]
[ext_resource type="PackedScene" uid="uid://cb5k3begpk4sc" path="res://src/win_ui/win_ui.tscn" id="7_win_ui"]
[ext_resource type="Script" uid="uid://dou1pba0f1mb3" path="res://src/level/conditions/all_players_dead_condition_system.gd" id="8_5yot2"]
[ext_resource type="PackedScene" uid="uid://bl20vi2va8lic" path="res://src/health/health_system.tscn" id="8_n4rap"]
[ext_resource type="Texture2D" uid="uid://bxu67k15ktat" path="res://assets/eraser.png" id="9_6smnk"]
[ext_resource type="PackedScene" uid="uid://cginrjmepqtfq" path="res://src/cog/cog_silver.tscn" id="9_p6v4a"]
[ext_resource type="PackedScene" uid="uid://b2qufv2y580sm" path="res://src/loss_ui/loss_ui.tscn" id="10_5suky"]
[ext_resource type="PackedScene" uid="uid://dg3necnhp81wv" path="res://src/level/level_state_component.tscn" id="10_k5trb"]
[ext_resource type="PackedScene" uid="uid://br7obebdd1opq" path="res://src/damage/damage_system.tscn" id="11_iv48k"]
[ext_resource type="Script" uid="uid://togcg2ol5h6u" path="res://src/level/level_state_data.gd" id="13_n4rap"]
[ext_resource type="PackedScene" uid="uid://dawp704yypk8x" path="res://src/level/outcome_system/level_outcome_system.tscn" id="15_5lobc"]
[ext_resource type="PackedScene" uid="uid://cwq6ex0lmj7al" path="res://src/paint/paint_system.tscn" id="16_paint_system"]
[ext_resource type="PackedScene" uid="uid://e0dt5jwwxndr" path="res://src/time_dilation/time_dilation_system.tscn" id="16_ww2lt"]
[ext_resource type="PackedScene" uid="uid://bi0dhs7jf7yrl" path="res://src/movement/movement_system.tscn" id="18_cwwql"]
[ext_resource type="PackedScene" uid="uid://se1apgxlfmx6" path="res://src/paint_refill/paint_refill_drop.tscn" id="18_paint_refill_drop"]
[ext_resource type="PackedScene" uid="uid://bnbdlymwq0s8q" path="res://src/water/water_system.tscn" id="19_cwwql"]
[ext_resource type="PackedScene" uid="uid://5agsxx7ekdi6" path="res://src/player/player_input_system.tscn" id="19_kd75s"]
[ext_resource type="PackedScene" uid="uid://cg0xsxrffpvqs" path="res://src/level/progress_system/level_progress_system.tscn" id="19_level_progress_system"]
[ext_resource type="PackedScene" uid="uid://dfy35tmjoeoht" path="res://src/water_tile/water_tile.tscn" id="21_kd75s"]
[ext_resource type="PackedScene" uid="uid://biw4o1x1u6l4i" path="res://src/tile_query_system/tile_query_system.tscn" id="23_ww2lt"]
[ext_resource type="PackedScene" uid="uid://d005t6u2v4w8" path="res://src/ability/wall_phase_system.tscn" id="24_wallphase"]
[ext_resource type="PackedScene" uid="uid://cne4tjc17alt0" path="res://src/ability/inverted_controls_system.tscn" id="25_inverted_controls"]
[ext_resource type="PackedScene" uid="uid://v4gsd8opi47x" path="res://src/cog/cog_bronze.tscn" id="29_o5bw5"]
[ext_resource type="PackedScene" uid="uid://bxy0123eraser" path="res://src/eraser/eraser.tscn" id="30_eraser"]
[ext_resource type="PackedScene" uid="uid://bly2dfc2jkil7" path="res://src/eraser/eraser_system.tscn" id="31_eraser_system"]
[ext_resource type="PackedScene" path="res://src/push/push_system.tscn" id="32_push_system"]

[sub_resource type="Resource" id="Resource_y4oqq"]
script = ExtResource("4_y4oqq")
progress_threshold = 0.3
scene_to_spawn = ExtResource("9_p6v4a")
icon = ExtResource("5_u1qsv")
metadata/_custom_type_script = "uid://d22krovyllkp2"

[sub_resource type="Resource" id="Resource_ww2lt"]
script = ExtResource("4_y4oqq")
progress_threshold = 0.6
scene_to_spawn = ExtResource("9_p6v4a")
icon = ExtResource("5_u1qsv")
metadata/_custom_type_script = "uid://d22krovyllkp2"

[sub_resource type="Resource" id="Resource_gj0ym"]
script = ExtResource("4_y4oqq")
progress_threshold = 0.9
scene_to_spawn = ExtResource("9_p6v4a")
icon = ExtResource("5_u1qsv")
metadata/_custom_type_script = "uid://d22krovyllkp2"

[sub_resource type="Resource" id="Resource_5yot2"]
script = ExtResource("4_y4oqq")
progress_threshold = 0.1
scene_to_spawn = ExtResource("29_o5bw5")
icon = ExtResource("7_vgudg")
metadata/_custom_type_script = "uid://d22krovyllkp2"

[sub_resource type="Resource" id="Resource_eraser_40"]
script = ExtResource("4_y4oqq")
progress_threshold = 0.05
scene_to_spawn = ExtResource("30_eraser")
icon = ExtResource("9_6smnk")
metadata/_custom_type_script = "uid://d22krovyllkp2"

[sub_resource type="Resource" id="Resource_eraser_70"]
script = ExtResource("4_y4oqq")
progress_threshold = 0.7
scene_to_spawn = ExtResource("30_eraser")
icon = ExtResource("9_6smnk")
metadata/_custom_type_script = "uid://d22krovyllkp2"

[sub_resource type="Resource" id="Resource_ascnc"]
script = ExtResource("13_n4rap")
events = Array[ExtResource("4_y4oqq")]([SubResource("Resource_y4oqq"), SubResource("Resource_ww2lt"), SubResource("Resource_gj0ym"), SubResource("Resource_5yot2"), SubResource("Resource_eraser_40"), SubResource("Resource_eraser_70")])
metadata/_custom_type_script = "uid://togcg2ol5h6u"

[sub_resource type="TileSetScenesCollectionSource" id="TileSetScenesCollectionSource_jqarl"]
scenes/1/scene = ExtResource("2_5vox1")
scenes/2/scene = ExtResource("21_kd75s")

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_3s6yk"]
texture = ExtResource("3_ob0n7")
texture_region_size = Vector2i(8, 8)
0:0/0 = 0
1:0/0 = 0
2:0/0 = 0
2:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
3:0/0 = 0
0:1/0 = 0
0:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
1:1/0 = 0
1:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
2:1/0 = 0
2:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
3:1/0 = 0
3:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)

[sub_resource type="TileSet" id="TileSet_rccf1"]
tile_size = Vector2i(8, 8)
physics_layer_0/collision_layer = 1
physics_layer_1/collision_layer = 2
physics_layer_1/collision_mask = 0
sources/1 = SubResource("TileSetAtlasSource_3s6yk")
sources/0 = SubResource("TileSetScenesCollectionSource_jqarl")

[node name="Level" type="Node2D" node_paths=PackedStringArray("level_state_component", "win_ui", "loss_ui", "tile_query_system")]
scale = Vector2(8, 8)
script = ExtResource("1_5vox1")
level_state_component = NodePath("Systems/LevelOutcomeSystem/LevelStateComponent")
win_ui = NodePath("WinUI")
loss_ui = NodePath("LossUI")
tile_query_system = NodePath("Systems/TileQuerySystem")

[node name="Systems" type="Node" parent="."]

[node name="LevelOutcomeSystem" parent="Systems" node_paths=PackedStringArray("level_state_component", "win_condition_systems", "loss_condition_systems") instance=ExtResource("15_5lobc")]
level_state_component = NodePath("LevelStateComponent")
win_condition_systems = [NodePath("WinConditions/PaintAllTilesConditionSystem")]
loss_condition_systems = [NodePath("LossConditions/AllPlayersDeadConditionSystem")]

[node name="LevelStateComponent" parent="Systems/LevelOutcomeSystem" instance=ExtResource("10_k5trb")]
data = SubResource("Resource_ascnc")

[node name="WinConditions" type="Node" parent="Systems/LevelOutcomeSystem"]

[node name="PaintAllTilesConditionSystem" type="Node" parent="Systems/LevelOutcomeSystem/WinConditions" node_paths=PackedStringArray("level_state_component")]
script = ExtResource("7_gj0ym")
level_state_component = NodePath("../../LevelStateComponent")

[node name="LossConditions" type="Node" parent="Systems/LevelOutcomeSystem"]

[node name="AllPlayersDeadConditionSystem" type="Node" parent="Systems/LevelOutcomeSystem/LossConditions" node_paths=PackedStringArray("health_system", "player_node")]
script = ExtResource("8_5yot2")
health_system = NodePath("../../../HealthSystem")
player_node = NodePath("../../../../Banana")

[node name="HealthSystem" parent="Systems" instance=ExtResource("8_n4rap")]

[node name="DamageSystem" parent="Systems" node_paths=PackedStringArray("health_system") instance=ExtResource("11_iv48k")]
health_system = NodePath("../HealthSystem")

[node name="PaintSystem" parent="Systems" node_paths=PackedStringArray("level_node", "tile_query_system", "level_progress_system") instance=ExtResource("16_paint_system")]
paint_refill_scene = ExtResource("18_paint_refill_drop")
level_node = NodePath("../..")
tile_query_system = NodePath("../TileQuerySystem")
level_progress_system = NodePath("../LevelProgressSystem")

[node name="LevelProgressSystem" parent="Systems" node_paths=PackedStringArray("level_state_component", "level_node", "player_node") instance=ExtResource("19_level_progress_system")]
level_state_component = NodePath("../LevelOutcomeSystem/LevelStateComponent")
level_node = NodePath("../..")
player_node = NodePath("../../Banana")
min_spawn_distance_from_player = 200.0

[node name="TimeDilationSystem" parent="Systems" node_paths=PackedStringArray("tile_query_system") instance=ExtResource("16_ww2lt")]
tile_query_system = NodePath("../TileQuerySystem")

[node name="TileQuerySystem" parent="Systems" instance=ExtResource("23_ww2lt")]

[node name="MovementSystem" parent="Systems" instance=ExtResource("18_cwwql")]

[node name="PlayerInputSystem" parent="Systems" node_paths=PackedStringArray("movement_system") instance=ExtResource("19_kd75s")]
movement_system = NodePath("../MovementSystem")

[node name="WallPhaseSystem" parent="Systems" node_paths=PackedStringArray("tile_query_system", "movement_system") instance=ExtResource("24_wallphase")]
tile_query_system = NodePath("../TileQuerySystem")
movement_system = NodePath("../MovementSystem")

[node name="WaterSystem" parent="Systems" node_paths=PackedStringArray("tile_query_system", "player", "player_paint_component", "player_movement_component", "player_hitbox") instance=ExtResource("19_cwwql")]
tile_query_system = NodePath("../TileQuerySystem")
player = NodePath("../../Banana")
player_paint_component = NodePath("../../Banana/PaintComponent")
player_movement_component = NodePath("../../Banana/MovementComponent")
player_hitbox = NodePath("../../Banana/Hitbox")

[node name="InvertedControlsSystem" parent="Systems" instance=ExtResource("25_inverted_controls")]

[node name="EraserSystem" parent="Systems" node_paths=PackedStringArray("movement_system") instance=ExtResource("31_eraser_system")]
movement_system = NodePath("../MovementSystem")

[node name="PushSystem" parent="Systems" node_paths=PackedStringArray("movement_system") instance=ExtResource("32_push_system")]
movement_system = NodePath("../MovementSystem")

[node name="TileMapLayer" type="TileMapLayer" parent="."]
texture_filter = 1
position = Vector2(0, 22.5)
tile_map_data = PackedByteArray("AAAAAAgAAQACAAAAAAAAAAkAAQACAAAAAAAAAAoAAQACAAAAAAAAAAsAAQACAAAAAAAAAAwAAQACAAAAAAAAAA0AAQACAAAAAAAAAA4AAQACAAAAAAAAAA8AAQACAAAAAAAAABAAAQACAAAAAAAAABEAAQACAAAAAAAAABIAAQACAAAAAAAAABMAAQACAAAAAAAAABQAAQACAAAAAAAAABUAAQACAAAAAAABAAgAAAAAAAAAAQABAAkAAAAAAAAAAQABAAoAAAAAAAAAAQABAAsAAAAAAAAAAQABAAwAAAAAAAAAAQABAA0AAQACAAAAAAABAA4AAQACAAAAAAABAA8AAAAAAAAAAQABABAAAAAAAAAAAQABABEAAAAAAAAAAQABABIAAAAAAAAAAQABABMAAAAAAAAAAQABABQAAAAAAAAAAQABABUAAQACAAAAAAACAAgAAAAAAAAAAQACAAkAAAAAAAAAAQACAAoAAAAAAAAAAQACAAsAAAAAAAAAAQACAAwAAAAAAAAAAQACAA0AAAAAAAAAAQACAA4AAAAAAAAAAQACAA8AAAAAAAAAAQACABAAAAAAAAAAAQACABEAAAAAAAAAAQACABIAAAAAAAAAAQACABMAAAAAAAAAAQACABQAAAAAAAAAAQACABUAAQACAAAAAAADAAgAAAAAAAAAAQADAAkAAAAAAAAAAQADAAoAAAAAAAAAAQADAAsAAAAAAAAAAQADAAwAAAAAAAAAAQADAA0AAAAAAAAAAQADAA4AAAAAAAAAAQADAA8AAAAAAAAAAQADABAAAAAAAAAAAQADABEAAAAAAAAAAQADABIAAAAAAAAAAQADABMAAAAAAAAAAQADABQAAAAAAAAAAQADABUAAQACAAAAAAAEAAgAAAAAAAAAAQAEAAkAAAAAAAAAAQAEAAoAAQACAAAAAAAEAAsAAQACAAAAAAAEAAwAAAAAAAAAAQAEAA0AAAAAAAAAAQAEAA4AAAAAAAAAAQAEAA8AAAAAAAAAAQAEABAAAQACAAAAAAAEABEAAQACAAAAAAAEABIAAAAAAAAAAQAEABMAAAAAAAAAAQAEABQAAAAAAAAAAQAEABUAAQACAAAAAAAFAAgAAAAAAAAAAQAFAAkAAAAAAAAAAQAFAAoAAQACAAAAAAAFAAsAAQACAAAAAAAFAAwAAAAAAAAAAQAFAA0AAAAAAAAAAQAFAA4AAAAAAAAAAQAFAA8AAAAAAAAAAQAFABAAAQACAAAAAAAFABEAAQACAAAAAAAFABIAAAAAAAAAAQAFABMAAAAAAAAAAQAFABQAAAAAAAAAAQAFABUAAQACAAAAAAAGAAgAAAAAAAAAAQAGAAkAAAAAAAAAAQAGAAoAAAAAAAAAAQAGAAsAAAAAAAAAAQAGAAwAAAAAAAAAAgAGAA0AAAAAAAAAAgAGAA4AAAAAAAAAAgAGAA8AAAAAAAAAAgAGABAAAAAAAAAAAQAGABEAAAAAAAAAAQAGABIAAAAAAAAAAQAGABMAAAAAAAAAAQAGABQAAAAAAAAAAQAGABUAAQACAAAAAAAHAAgAAQAAAAEAAAAHAAkAAQACAAEAAAAHAAoAAAAAAAAAAQAHAAsAAAAAAAAAAQAHAAwAAAAAAAAAAgAHAA0AAAAAAAAAAgAHAA4AAAAAAAAAAgAHAA8AAAAAAAAAAgAHABAAAAAAAAAAAQAHABEAAAAAAAAAAQAHABIAAQAAAAEAAAAHABMAAQACAAEAAAAHABQAAAAAAAAAAQAHABUAAQACAAAAAAAIAAgAAQABAAEAAAAIAAkAAQADAAEAAAAIAAoAAAAAAAAAAQAIAAsAAAAAAAAAAQAIAAwAAAAAAAAAAgAIAA0AAAAAAAAAAgAIAA4AAAAAAAAAAgAIAA8AAAAAAAAAAgAIABAAAAAAAAAAAQAIABEAAAAAAAAAAQAIABIAAQABAAEAAAAIABMAAQADAAEAAAAIABQAAAAAAAAAAQAIABUAAQACAAAAAAAJAAgAAQABAAEAAAAJAAkAAQADAAEAAAAJAAoAAAAAAAAAAQAJAAsAAAAAAAAAAQAJAAwAAAAAAAAAAgAJAA0AAAAAAAAAAgAJAA4AAAAAAAAAAgAJAA8AAAAAAAAAAgAJABAAAAAAAAAAAQAJABEAAAAAAAAAAQAJABIAAQABAAEAAAAJABMAAQADAAEAAAAJABQAAAAAAAAAAQAJABUAAQACAAAAAAAKAAgAAAAAAAAAAQAKAAkAAAAAAAAAAQAKAAoAAAAAAAAAAQAKAAsAAAAAAAAAAQAKAAwAAAAAAAAAAgAKAA0AAAAAAAAAAgAKAA4AAAAAAAAAAgAKAA8AAAAAAAAAAgAKABAAAAAAAAAAAQAKABEAAAAAAAAAAQAKABIAAAAAAAAAAQAKABMAAAAAAAAAAQAKABQAAAAAAAAAAQAKABUAAQACAAAAAAALAAgAAAAAAAAAAQALAAkAAAAAAAAAAQALAAoAAQACAAAAAAALAAsAAQACAAAAAAALAAwAAAAAAAAAAQALAA0AAAAAAAAAAQALAA4AAAAAAAAAAQALAA8AAAAAAAAAAQALABAAAQACAAAAAAALABEAAQACAAAAAAALABIAAAAAAAAAAQALABMAAAAAAAAAAQALABQAAAAAAAAAAQALABUAAQACAAAAAAAMAAgAAAAAAAAAAQAMAAkAAAAAAAAAAQAMAAoAAQACAAAAAAAMAAsAAQACAAAAAAAMAAwAAAAAAAAAAQAMAA0AAAAAAAAAAQAMAA4AAAAAAAAAAQAMAA8AAAAAAAAAAQAMABAAAQACAAAAAAAMABEAAQACAAAAAAAMABIAAAAAAAAAAQAMABMAAAAAAAAAAQAMABQAAAAAAAAAAQAMABUAAQACAAAAAAANAAgAAAAAAAAAAQANAAkAAAAAAAAAAQANAAoAAAAAAAAAAQANAAsAAAAAAAAAAQANAAwAAAAAAAAAAQANAA0AAAAAAAAAAQANAA4AAAAAAAAAAQANAA8AAAAAAAAAAQANABAAAAAAAAAAAQANABEAAAAAAAAAAQANABIAAAAAAAAAAQANABMAAAAAAAAAAQANABQAAAAAAAAAAQANABUAAQACAAAAAAAOAAgAAAAAAAAAAQAOAAkAAAAAAAAAAQAOAAoAAAAAAAAAAQAOAAsAAAAAAAAAAQAOAAwAAAAAAAAAAQAOAA0AAAAAAAAAAQAOAA4AAAAAAAAAAQAOAA8AAAAAAAAAAQAOABAAAAAAAAAAAQAOABEAAAAAAAAAAQAOABIAAAAAAAAAAQAOABMAAAAAAAAAAQAOABQAAAAAAAAAAQAOABUAAQACAAAAAAAPAAgAAAAAAAAAAQAPAAkAAAAAAAAAAQAPAAoAAAAAAAAAAQAPAAsAAAAAAAAAAQAPAAwAAAAAAAAAAQAPAA0AAQACAAAAAAAPAA4AAQACAAAAAAAPAA8AAAAAAAAAAQAPABAAAAAAAAAAAQAPABEAAAAAAAAAAQAPABIAAAAAAAAAAQAPABMAAAAAAAAAAQAPABQAAAAAAAAAAQAPABUAAQACAAAAAAAQAAgAAQACAAAAAAAQAAkAAQACAAAAAAAQAAoAAQACAAAAAAAQAAsAAQACAAAAAAAQAAwAAQACAAAAAAAQAA0AAQACAAAAAAAQAA4AAQACAAAAAAAQAA8AAQACAAAAAAAQABAAAQACAAAAAAAQABEAAQACAAAAAAAQABIAAQACAAAAAAAQABMAAQACAAAAAAAQABQAAQACAAAAAAAQABUAAQACAAAAAAAAAAYAAQACAAAAAAAAAAcAAQACAAAAAAABAAYAAQACAAAAAAABAAcAAAAAAAAAAQACAAYAAQACAAAAAAACAAcAAAAAAAAAAQADAAYAAQACAAAAAAADAAcAAAAAAAAAAQAEAAYAAQACAAAAAAAEAAcAAAAAAAAAAQAFAAYAAQACAAAAAAAFAAcAAAAAAAAAAQAGAAYAAQACAAAAAAAGAAcAAAAAAAAAAQAHAAYAAQACAAAAAAAHAAcAAAAAAAAAAQAIAAYAAQACAAAAAAAIAAcAAAAAAAAAAQAJAAYAAQACAAAAAAAJAAcAAAAAAAAAAQAKAAYAAQACAAAAAAAKAAcAAAAAAAAAAQALAAYAAQACAAAAAAALAAcAAAAAAAAAAQAMAAYAAQACAAAAAAAMAAcAAAAAAAAAAQANAAYAAQACAAAAAAANAAcAAAAAAAAAAQAOAAYAAQACAAAAAAAOAAcAAAAAAAAAAQAPAAYAAQACAAAAAAAPAAcAAAAAAAAAAQAQAAYAAQACAAAAAAAQAAcAAQACAAAAAAA=")
tile_set = SubResource("TileSet_rccf1")

[node name="PaintBar" parent="." node_paths=PackedStringArray("paint_component") instance=ExtResource("4_p6v4a")]
position = Vector2(70.375, 206.25)
paint_component = NodePath("../Banana/PaintComponent")

[node name="ProgressBar" parent="." node_paths=PackedStringArray("level_state_component") instance=ExtResource("5_5suky")]
position = Vector2(8.5, 54.5)
level_state_component = NodePath("../Systems/LevelOutcomeSystem/LevelStateComponent")

[node name="Banana" parent="." instance=ExtResource("6_k5trb")]
position = Vector2(44, 134.5)

[node name="Cog" parent="." instance=ExtResource("9_p6v4a")]
position = Vector2(90.5, 133)

[node name="WinUI" parent="." instance=ExtResource("7_win_ui")]
visible = false

[node name="LossUI" parent="." instance=ExtResource("10_5suky")]
visible = false

[node name="CogBronze" parent="." instance=ExtResource("29_o5bw5")]
position = Vector2(22.5, 180.125)

[editable path="Banana"]
