class_name LevelProgressSystem
extends Node

@export var level_state_component: LevelStateComponent
@export var level_node: Node2D
@export var player_node: Node2D
@export var min_spawn_distance_from_player: float = 40.0

func _ready() -> void:
	level_state_component.data.reset()

func _process(_delta: float) -> void:
	var data: LevelStateData = level_state_component.data
	if data.state != LevelStateData.State.PLAYING or data.events.is_empty():
		return

	var current_progress: float = data.get_progress()

	for i in range(data.events.size()):
		var event: LevelEventData = data.events[i]
		var is_triggered: bool = data._triggered_events[i]

		if not is_triggered and current_progress >= event.progress_threshold:
			_trigger_event(event)
			data.trigger_event(i)

func get_safe_tiles() -> Array[Node2D]:
	var safe_tiles: Array[Node2D] = []
	if not is_instance_valid(player_node):
		return safe_tiles

	var all_tiles: Array[Node] = get_tree().get_nodes_in_group(&"color_tiles")
	var player_position: Vector2 = player_node.global_position

	for tile in all_tiles:
		var tile_node: Node2D = tile as Node2D
		if tile_node.global_position.distance_to(player_position) > min_spawn_distance_from_player:
			safe_tiles.append(tile_node)
	return safe_tiles

func _trigger_event(event_data: LevelEventData) -> void:
	if event_data.scene_to_spawn != null:
		_spawn_scene_at_safe_tile(event_data.scene_to_spawn)

func _spawn_scene_at_safe_tile(scene: PackedScene) -> void:
	if level_node == null:
		return

	var safe_tiles: Array[Node2D] = get_safe_tiles()
	if safe_tiles.is_empty():
		push_warning("LevelProgressSystem: No safe tiles found to spawn scene.")
		return

	var random_tile: Node2D = safe_tiles.pick_random()
	var spawn_position: Vector2 = random_tile.global_position

	var new_instance: Node2D = scene.instantiate() as Node2D
	level_node.add_child(new_instance)
	new_instance.global_position = spawn_position
