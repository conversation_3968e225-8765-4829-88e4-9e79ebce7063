[gd_resource type="Resource" script_class_name="LevelStateData" load_steps=7 format=3 uid="uid://d0e1f2g3h4i5j6k7l8m9n0o1p2q3r4s5"]

[ext_resource type="Script" path="res://src/level/level_state_data.gd" id="1_tuvwxyz"]
[ext_resource type="Resource" path="res://src/level/conditions/win_by_painting.tres" id="2_abcdefg"]
[ext_resource type="Resource" path="res://src/level/conditions/loss_by_death.tres" id="3_hijklmn"]
[ext_resource type="Resource" path="res://src/level/events_data/spawn_cog_25.tres" id="4_spawn_cog_25"]
[ext_resource type="Resource" path="res://src/level/events_data/spawn_cog_50.tres" id="5_spawn_cog_50"]
[ext_resource type="Resource" path="res://src/level/events_data/spawn_cog_75.tres" id="6_spawn_cog_75"]

[resource]
script = ExtResource("1_tuvwxyz")
win_conditions = Array[Resource]([ExtResource("2_abcdefg")])
loss_conditions = Array[Resource]([ExtResource("3_hijklmn")])
events = Array[Resource]([ExtResource("4_spawn_cog_25"), ExtResource("5_spawn_cog_50"), ExtResource("6_spawn_cog_75")])
total_tiles = 0
state = 0
painted_tiles = 0
