class_name PlayerInputSystem
extends Node

@export var movement_system: MovementSystem
@export var swipe_threshold: float = 50.0
@export var touch_hold_threshold: float = 100.0

var _player_movement_component: MovementComponent
var _player_ability_component: AbilityComponent

var _held_direction: Vector2 = Vector2.ZERO
var _key_hold_timer: float = 0.0
var _repeat_mode_active: bool = false
var _movement_cooldown: float = 0.0

var _is_touching: bool = false
var _touch_start_position: Vector2 = Vector2.ZERO
var _touch_direction: Vector2 = Vector2.ZERO
var _touch_hold_timer: float = 0.0
var _touch_repeat_mode: bool = false

const INITIAL_REPEAT_DELAY: float = 0.3
const REPEAT_RATE: float = 0.2

func _ready() -> void:
	_find_player_component.call_deferred()

func _find_player_component() -> void:
	var player_nodes: Array[Node] = get_tree().get_nodes_in_group(&"player")
	if not player_nodes.is_empty():
		var player_node: Node = player_nodes[0]
		_player_movement_component = player_node.find_child(&"MovementComponent", true, false)
		_player_ability_component = player_node.find_child(&"AbilityComponent", true, false)

func _unhandled_input(event: InputEvent) -> void:
	if not is_instance_valid(_player_movement_component) or not is_instance_valid(movement_system):
		return

	if event.is_action_pressed(&"move_left"):
		_handle_movement_input(Vector2.LEFT)
	elif event.is_action_pressed(&"move_right"):
		_handle_movement_input(Vector2.RIGHT)
	elif event.is_action_pressed(&"move_up"):
		_handle_movement_input(Vector2.UP)
	elif event.is_action_pressed(&"move_down"):
		_handle_movement_input(Vector2.DOWN)

	var inversion_multiplier: float = _get_inversion_multiplier()
	if event.is_action_released(&"move_left") and _held_direction == Vector2.LEFT * inversion_multiplier:
		_held_direction = Vector2.ZERO
	elif event.is_action_released(&"move_right") and _held_direction == Vector2.RIGHT * inversion_multiplier:
		_held_direction = Vector2.ZERO
	elif event.is_action_released(&"move_up") and _held_direction == Vector2.UP * inversion_multiplier:
		_held_direction = Vector2.ZERO
	elif event.is_action_released(&"move_down") and _held_direction == Vector2.DOWN * inversion_multiplier:
		_held_direction = Vector2.ZERO

	if event is InputEventScreenTouch:
		_handle_touch_input(event as InputEventScreenTouch)
	elif event is InputEventScreenDrag:
		_handle_drag_input(event as InputEventScreenDrag)

func _process(delta: float) -> void:
	if not is_instance_valid(_player_movement_component):
		return

	if _movement_cooldown > 0.0:
		_movement_cooldown -= delta

	if _player_movement_component.data != null and _player_movement_component.data.is_moving:
		return

	if _held_direction != Vector2.ZERO and _movement_cooldown <= 0.0:
		_key_hold_timer += delta
		if not _repeat_mode_active and _key_hold_timer >= INITIAL_REPEAT_DELAY:
			_repeat_mode_active = true
			_key_hold_timer = 0.0
			movement_system.move(_player_movement_component, _held_direction)
			_movement_cooldown = REPEAT_RATE
		elif _repeat_mode_active and _key_hold_timer >= REPEAT_RATE:
			_key_hold_timer = 0.0
			movement_system.move(_player_movement_component, _held_direction)
			_movement_cooldown = REPEAT_RATE

	if _is_touching and _touch_direction != Vector2.ZERO and _movement_cooldown <= 0.0:
		_touch_hold_timer += delta
		if not _touch_repeat_mode and _touch_hold_timer >= INITIAL_REPEAT_DELAY:
			_touch_repeat_mode = true
			_touch_hold_timer = 0.0
			movement_system.move(_player_movement_component, _touch_direction)
			_movement_cooldown = REPEAT_RATE
		elif _touch_repeat_mode and _touch_hold_timer >= REPEAT_RATE:
			_touch_hold_timer = 0.0
			movement_system.move(_player_movement_component, _touch_direction)
			_movement_cooldown = REPEAT_RATE

func _handle_movement_input(direction: Vector2) -> void:
	var final_direction: Vector2 = direction * _get_inversion_multiplier()

	_held_direction = final_direction
	_key_hold_timer = 0.0
	_repeat_mode_active = false

	if _player_movement_component.data == null:
		return

	if not _player_movement_component.data.is_moving and _movement_cooldown <= 0.0:
		movement_system.move(_player_movement_component, _held_direction)
		_movement_cooldown = REPEAT_RATE

func _handle_touch_input(event: InputEventScreenTouch) -> void:
	if event.pressed:
		_is_touching = true
		_touch_start_position = event.position
		_touch_direction = Vector2.ZERO
		_touch_hold_timer = 0.0
		_touch_repeat_mode = false
	else:
		if _is_touching:
			var delta_vec: Vector2 = event.position - _touch_start_position
			if delta_vec.length() >= swipe_threshold:
				var swipe_dir: Vector2 = _get_swipe_direction(delta_vec) * _get_inversion_multiplier()
				if swipe_dir != Vector2.ZERO and _movement_cooldown <= 0.0 and (_player_movement_component.data == null or not _player_movement_component.data.is_moving):
					movement_system.move(_player_movement_component, swipe_dir)
					_movement_cooldown = REPEAT_RATE
		_is_touching = false
		_touch_direction = Vector2.ZERO
		_touch_repeat_mode = false

func _handle_drag_input(event: InputEventScreenDrag) -> void:
	if not _is_touching:
		return

	var delta_vec: Vector2 = event.position - _touch_start_position
	if delta_vec.length() >= touch_hold_threshold:
		var hold_dir: Vector2 = _get_swipe_direction(delta_vec) * _get_inversion_multiplier()
		if hold_dir != Vector2.ZERO and hold_dir != _touch_direction:
			_touch_direction = hold_dir
			_touch_hold_timer = 0.0
			_touch_repeat_mode = false
			_touch_start_position = event.position

			if _movement_cooldown <= 0.0 and (_player_movement_component.data == null or not _player_movement_component.data.is_moving):
				movement_system.move(_player_movement_component, _touch_direction)
				_movement_cooldown = REPEAT_RATE

func _get_swipe_direction(delta_vec: Vector2) -> Vector2:
	var a: Vector2 = delta_vec.abs()
	if a.x > a.y:
		return Vector2.RIGHT if delta_vec.x > 0.0 else Vector2.LEFT
	else:
		return Vector2.DOWN if delta_vec.y > 0.0 else Vector2.UP

func _get_inversion_multiplier() -> float:
	if not is_instance_valid(_player_ability_component):
		return 1.0

	for ability: AbilityData in _player_ability_component.abilities:
		if ability is InvertedControlsAbilityData:
			return -1.0

	return 1.0
