class_name EraserSystem
extends Node

@export var movement_system: MovementSystem

func _process(delta: float) -> void:
	var erasers: Array[Node] = get_tree().get_nodes_in_group(&"erasers")
	for eraser_node: Node in erasers:
		var eraser_component: EraserComponent = eraser_node as EraserComponent
		if eraser_component != null:
			_tick_eraser(eraser_component, delta)

func _tick_eraser(eraser_component: EraserComponent, delta: float) -> void:
	if eraser_component.data == null:
		return

	var eraser_actor: CharacterBody2D = eraser_component.get_parent() as CharacterBody2D
	if eraser_actor == null:
		return

	var movement_component: MovementComponent = eraser_actor.find_child(&"MovementComponent", true, false)
	if movement_component == null or movement_component.data == null:
		return

	var data: EraserData = eraser_component.data
	var movement_data: MovementData = movement_component.data

	var scaled_delta: float = TimeScaleUtil.scale_delta(eraser_component, delta)
	eraser_component.step_cooldown = max(0.0, eraser_component.step_cooldown - scaled_delta)
	eraser_component.retarget_timer += scaled_delta

	var nearby_saw: Node2D = _find_nearby_saw(eraser_actor, data.flee_radius)
	if nearby_saw != null:
		_handle_flee_state(eraser_component, eraser_actor, movement_component, nearby_saw)
		return

	var current_tile: ColorTile = _get_tile_at_position(eraser_actor.global_position)
	if current_tile != null and current_tile.is_painted():
		_handle_erase_state(eraser_component, current_tile, delta)
		return

	if eraser_component.retarget_timer >= data.retarget_interval:
		eraser_component.target_tile = _find_nearest_painted_tile(eraser_actor)
		eraser_component.retarget_timer = 0.0

	if eraser_component.target_tile != null and not movement_data.is_moving and eraser_component.step_cooldown <= 0.0:
		_handle_seek_state(eraser_component, eraser_actor, movement_component)

func _handle_flee_state(eraser_component: EraserComponent, eraser_actor: CharacterBody2D, movement_component: MovementComponent, saw: Node2D) -> void:
	eraser_component.state = EraserComponent.State.FLEE
	eraser_component.erase_timer = 0.0

	if movement_component.data.is_moving or eraser_component.step_cooldown > 0.0:
		return

	var flee_direction: Vector2 = (eraser_actor.global_position - saw.global_position).normalized()
	var step_direction: Vector2 = _choose_step_direction(flee_direction)

	if step_direction != Vector2.ZERO:
		movement_system.move(movement_component, step_direction)
		var next_interval: float = movement_component.data.move_duration + eraser_component.data.step_pause
		eraser_component.step_cooldown = next_interval

func _handle_erase_state(eraser_component: EraserComponent, tile: ColorTile, delta: float) -> void:
	eraser_component.state = EraserComponent.State.ERASE

	var scaled_delta: float = TimeScaleUtil.scale_delta(eraser_component, delta)
	eraser_component.erase_timer += scaled_delta

	if eraser_component.erase_timer >= eraser_component.data.erase_duration:
		tile.unpaint()
		eraser_component.erase_timer = 0.0
		eraser_component.state = EraserComponent.State.SEEK

func _handle_seek_state(eraser_component: EraserComponent, eraser_actor: CharacterBody2D, movement_component: MovementComponent) -> void:
	eraser_component.state = EraserComponent.State.SEEK
	eraser_component.erase_timer = 0.0

	var direction: Vector2 = _choose_step_towards(eraser_actor.global_position, eraser_component.target_tile.global_position)
	if direction != Vector2.ZERO:
		movement_system.move(movement_component, direction)
		var next_interval: float = movement_component.data.move_duration + eraser_component.data.step_pause
		eraser_component.step_cooldown = next_interval

func _find_nearby_saw(eraser_pos: Node2D, radius: float) -> Node2D:
	var saws: Array[Node] = get_tree().get_nodes_in_group(&"saws")
	for saw_node: Node in saws:
		var saw: Node2D = saw_node as Node2D
		if saw != null and eraser_pos.global_position.distance_to(saw.global_position) <= radius:
			return saw
	return null

func _find_nearest_painted_tile(eraser_actor: CharacterBody2D) -> ColorTile:
	var tiles: Array[Node] = get_tree().get_nodes_in_group(&"color_tiles")
	var nearest_tile: ColorTile = null
	var min_distance: float = INF

	for tile_node: Node in tiles:
		var tile: ColorTile = tile_node as ColorTile
		if tile != null and tile.is_painted():
			var distance: float = eraser_actor.global_position.distance_to(tile.global_position)
			if distance < min_distance:
				min_distance = distance
				nearest_tile = tile

	return nearest_tile

func _get_tile_at_position(pos: Vector2) -> ColorTile:
	var tiles: Array[Node] = get_tree().get_nodes_in_group(&"color_tiles")
	for tile_node: Node in tiles:
		var tile: ColorTile = tile_node as ColorTile
		if tile != null and tile.global_position.distance_to(pos) < 4.0:
			return tile
	return null

func _choose_step_towards(from: Vector2, to: Vector2) -> Vector2:
	var delta_vec: Vector2 = to - from
	if abs(delta_vec.x) > abs(delta_vec.y):
		return Vector2(1.0 if delta_vec.x > 0 else -1.0, 0.0)
	else:
		return Vector2(0.0, 1.0 if delta_vec.y > 0 else -1.0)

func _choose_step_direction(direction: Vector2) -> Vector2:
	if abs(direction.x) > abs(direction.y):
		return Vector2(1.0 if direction.x > 0 else -1.0, 0.0)
	else:
		return Vector2(0.0, 1.0 if direction.y > 0 else -1.0)
