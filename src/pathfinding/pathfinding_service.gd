class_name PathfindingService
extends Node

signal path_computed(start_world: Vector2, goal_world: Vector2, points_world: Array[Vector2], expanded_nodes: int)

@export var cfg: GridNavConfig
@export var tile_query_system: TileQuerySystem
@export var collision_reference_ray: RayCast2D

var astar: AStarGrid2D
var builder: GridGraphBuilder

var _cache: Dictionary = {}

func _ready() -> void:
	if cfg == null:
		cfg = GridNavConfig.new()

	if collision_reference_ray != null:
		cfg.collision_mask = collision_reference_ray.collision_mask

	builder = GridGraphBuilder.new()
	add_child(builder)

	var tiles: Array[Node] = get_tree().get_nodes_in_group("color_tiles")
	var tile_nodes: Array[Node2D] = []
	for n in tiles:
		if n is Node2D:
			tile_nodes.append(n as Node2D)

	astar = builder.rebuild_from_tiles(tile_nodes, cfg)
	add_to_group("pathfinding_service")
	# Try to auto-wire a debug overlay if present
	var overlays := get_tree().get_nodes_in_group("nav_debug_overlay")
	if overlays.size() > 0:
		var overlay: NavDebugOverlay = overlays[0] as NavDebugOverlay
		if overlay != null:
			overlay.set_sources(astar, cfg)
			path_computed.connect(func(s: Vector2, g: Vector2, pts: Array[Vector2], n: int) -> void: overlay.set_latest_path(pts))

func get_path_world(start_world: Vector2, goal_world: Vector2, max_nodes: int = -1) -> Array[Vector2]:
	if astar == null:
		return []

	var s_base := builder.world_to_base_cell(start_world)
	var g_base := builder.world_to_base_cell(goal_world)

	if s_base.x < 0 or s_base.y < 0 or g_base.x < 0 or g_base.y < 0:
		return []
	if s_base == g_base:
		return [builder.base_to_world_center(s_base)]

	var key := _make_key(s_base, g_base)
	var now_s: float = Time.get_ticks_msec() / 1000.0
	if _cache.has(key):
		var entry: Dictionary = _cache[key]
		if entry.expires_at > now_s:
			return entry.path.duplicate(true)
		else:
			_cache.erase(key)

	var from_up := GridGraphBuilder.base_to_upscaled(s_base)
	var to_up := GridGraphBuilder.base_to_upscaled(g_base)

	# Acquire path in upscaled grid space
	var up_path: PackedVector2Array = astar.get_point_path(from_up, to_up)
	if up_path.is_empty():
		# Cache empty result briefly to avoid hammering
		_cache[key] = {"path": [], "expires_at": now_s + cfg.repath_cooldown_s}
		return []

	# Filter only base cell centers (odd, odd) and collapse duplicates
	var base_seq: Array[Vector2i] = []
	for p: Vector2 in up_path:
		var ix: int = int(round(p.x))
		var iy: int = int(round(p.y))
		if (ix & 1) == 1 and (iy & 1) == 1:
			var b := Vector2i((ix - 1) / 2, (iy - 1) / 2)
			if base_seq.is_empty() or base_seq.back() != b:
				base_seq.append(b)

	if base_seq.is_empty():
		_cache[key] = {"path": [], "expires_at": now_s + cfg.repath_cooldown_s}
		return []

	var world_path: Array[Vector2] = []
	for b in base_seq:
		world_path.append(builder.base_to_world_center(b))

	_cache[key] = {"path": world_path.duplicate(true), "expires_at": now_s + cfg.path_ttl_s}
	path_computed.emit(start_world, goal_world, world_path, 0)
	return world_path

func invalidate_region(region_world: Rect2) -> void:
	# For the minimal version, simply clear the cache.
	_cache.clear()

func _make_key(a: Vector2i, b: Vector2i) -> String:
	return str(a.x, ",", a.y, "-", b.x, ",", b.y)
